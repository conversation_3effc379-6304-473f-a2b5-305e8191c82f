const express = require('express');
const router = express.Router();
const User = require('../models/User');
const Subscription = require('../models/Subscription');
const Permission = require('../models/Permission');
const QuotaMiddleware = require('../middleware/quotaMiddleware');
const { getSystemStats: getSystemMonitorStats } = require('../services/systemMonitor');

// Middleware to check if user is admin
const requireAdmin = Permission.requireRole('admin');

// Admin dashboard
router.get('/dashboard', requireAdmin, async (req, res) => {
  try {
    const users = await User.findAll(10, 0);
    const plans = await Subscription.getAllPlansWithSubscribers();

    // Get system statistics
    const stats = await getSystemStats();

    // Get system monitoring stats
    const systemStats = await getSystemMonitorStats();

    res.render('admin/dashboard', {
      title: 'Admin Dashboard',
      active: 'admin',
      users,
      plans,
      stats,
      systemStats
    });
  } catch (error) {
    console.error('Admin dashboard error:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load admin dashboard',
      error: error
    });
  }
});

// User management
router.get('/users', requireAdmin, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = 20;
    const offset = (page - 1) * limit;

    const users = await User.findAll(limit, offset);
    const roles = await Permission.getAllRoles();
    const plans = await Subscription.getAllPlans();
    const stats = await getSystemStats();

    res.render('admin/users', {
      title: 'User Management',
      active: 'admin',
      users,
      roles,
      plans,
      stats,
      currentPage: page
    });
  } catch (error) {
    console.error('User management error:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load user management',
      error: error
    });
  }
});

// Update user role
router.post('/users/:userId/role', requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const { role } = req.body;

    await User.updateRole(userId, role);

    res.json({ success: true, message: 'User role updated successfully' });
  } catch (error) {
    console.error('Update user role error:', error);
    res.status(500).json({ error: 'Failed to update user role' });
  }
});

// Update user plan
router.post('/users/:userId/plan', requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const { planId } = req.body;

    const plan = await Subscription.getPlanById(planId);
    if (!plan) {
      return res.status(404).json({ error: 'Plan not found' });
    }

    await User.updatePlan(userId, plan.name, plan.max_streaming_slots, plan.max_storage_gb);

    res.json({ success: true, message: 'User plan updated successfully' });
  } catch (error) {
    console.error('Update user plan error:', error);
    res.status(500).json({ error: 'Failed to update user plan' });
  }
});

// Toggle user active status
router.post('/users/toggle-status', requireAdmin, async (req, res) => {
  try {
    const { userId, isActive } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    await User.updateActiveStatus(userId, isActive);

    res.json({ success: true, message: 'User status updated successfully' });
  } catch (error) {
    console.error('Toggle user status error:', error);
    res.status(500).json({ error: 'Failed to update user status' });
  }
});

// Subscription plans management
router.get('/plans', requireAdmin, async (req, res) => {
  try {
    const plans = await Subscription.getAllPlansWithSubscribers();

    res.render('admin/plans', {
      title: 'Subscription Plans',
      active: 'admin',
      plans
    });
  } catch (error) {
    console.error('Plans management error:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load plans management',
      error: error
    });
  }
});

// Create new subscription plan
router.post('/plans/create', requireAdmin, async (req, res) => {
  try {
    const { name, price, currency, billing_period, max_streaming_slots, max_storage_gb, features } = req.body;

    if (!name || price === undefined || price === null || price === '') {
      return res.status(400).json({ error: 'Plan name and price are required' });
    }

    // Convert price to number and validate
    const numericPrice = parseFloat(price);
    if (isNaN(numericPrice) || numericPrice < 0) {
      return res.status(400).json({ error: 'Price must be a valid number (0 or greater)' });
    }

    // Check if plan name already exists (only check active plans)
    const existingPlan = await Subscription.getPlanByName(name.trim());
    console.log(`Checking plan name "${name.trim()}" - Found existing:`, existingPlan ? existingPlan.id : 'none');
    if (existingPlan) {
      return res.status(400).json({
        error: 'A plan with this name already exists',
        details: `Plan "${name}" already exists with ID: ${existingPlan.id}`
      });
    }

    // Validate and convert streaming slots
    const numericSlots = max_streaming_slots !== undefined && max_streaming_slots !== '' ? parseInt(max_streaming_slots) : 1;
    if (isNaN(numericSlots) || numericSlots < -1) {
      return res.status(400).json({ error: 'Max streaming slots must be a valid number (-1 for unlimited, 0 or greater)' });
    }

    // Validate and convert storage (handle both old format and new format)
    let numericStorage;
    if (req.body.max_storage_value !== undefined && req.body.storage_unit !== undefined) {
      // New format with unit selection
      const storageValue = parseFloat(req.body.max_storage_value);
      const storageUnit = req.body.storage_unit;

      if (isNaN(storageValue) || storageValue < 0) {
        return res.status(400).json({ error: 'Max storage must be a valid number (0 or greater)' });
      }

      numericStorage = storageUnit === 'mb' ? storageValue / 1024 : storageValue;
    } else {
      // Old format (GB only)
      numericStorage = max_storage_gb !== undefined && max_storage_gb !== '' ? parseFloat(max_storage_gb) : 5;
      if (isNaN(numericStorage) || numericStorage < 0) {
        return res.status(400).json({ error: 'Max storage must be a valid number (0 or greater)' });
      }
    }

    const planData = {
      name,
      price: numericPrice,
      currency: currency || 'USD',
      billing_period: billing_period || 'monthly',
      max_streaming_slots: numericSlots,
      max_storage_gb: numericStorage,
      features: Array.isArray(features) ? features : []
    };

    const plan = await Subscription.createPlan(planData);

    res.json({ success: true, message: 'Plan created successfully', plan });
  } catch (error) {
    console.error('Create plan error:', error);
    res.status(500).json({ error: 'Failed to create plan' });
  }
});

// Update subscription plan
router.post('/plans/:planId/edit', requireAdmin, async (req, res) => {
  try {
    const { planId } = req.params;
    const { name, price, currency, billing_period, max_streaming_slots, max_storage_gb, features } = req.body;

    if (!name || price === undefined || price === null || price === '') {
      return res.status(400).json({ error: 'Plan name and price are required' });
    }

    // Convert price to number and validate
    const numericPrice = parseFloat(price);
    if (isNaN(numericPrice) || numericPrice < 0) {
      return res.status(400).json({ error: 'Price must be a valid number (0 or greater)' });
    }

    // Check if plan name already exists (excluding current plan, only check active plans)
    const existingPlan = await Subscription.getPlanByName(name.trim());
    console.log(`Editing plan ${planId} - Checking name "${name.trim()}" - Found existing:`, existingPlan ? existingPlan.id : 'none');
    if (existingPlan && existingPlan.id !== planId) {
      return res.status(400).json({
        error: 'A plan with this name already exists',
        details: `Plan "${name}" already exists with ID: ${existingPlan.id}. Current plan ID: ${planId}`
      });
    }

    // Validate and convert streaming slots
    const numericSlots = max_streaming_slots !== undefined && max_streaming_slots !== '' ? parseInt(max_streaming_slots) : 1;
    if (isNaN(numericSlots) || numericSlots < -1) {
      return res.status(400).json({ error: 'Max streaming slots must be a valid number (-1 for unlimited, 0 or greater)' });
    }

    // Validate and convert storage (handle both old format and new format)
    let numericStorage;
    if (req.body.max_storage_value !== undefined && req.body.storage_unit !== undefined) {
      // New format with unit selection
      const storageValue = parseFloat(req.body.max_storage_value);
      const storageUnit = req.body.storage_unit;

      if (isNaN(storageValue) || storageValue < 0) {
        return res.status(400).json({ error: 'Max storage must be a valid number (0 or greater)' });
      }

      numericStorage = storageUnit === 'mb' ? storageValue / 1024 : storageValue;
    } else {
      // Old format (GB only)
      numericStorage = max_storage_gb !== undefined && max_storage_gb !== '' ? parseFloat(max_storage_gb) : 5;
      if (isNaN(numericStorage) || numericStorage < 0) {
        return res.status(400).json({ error: 'Max storage must be a valid number (0 or greater)' });
      }
    }

    const planData = {
      name,
      price: numericPrice,
      currency: currency || 'USD',
      billing_period: billing_period || 'monthly',
      max_streaming_slots: numericSlots,
      max_storage_gb: numericStorage,
      features: Array.isArray(features) ? features : []
    };

    const plan = await Subscription.updatePlan(planId, planData);

    res.json({ success: true, message: 'Plan updated successfully', plan });
  } catch (error) {
    console.error('Update plan error:', error);
    res.status(500).json({ error: 'Failed to update plan' });
  }
});

// Delete subscription plan
router.post('/plans/:planId/delete', requireAdmin, async (req, res) => {
  try {
    const { planId } = req.params;

    // Check if plan exists
    const plan = await Subscription.getPlanById(planId);
    if (!plan) {
      return res.status(404).json({ error: 'Plan not found' });
    }

    // Don't allow deleting the free plan
    if (plan.name.toLowerCase() === 'free' || plan.name.toLowerCase().includes('free')) {
      return res.status(400).json({ error: 'Cannot delete the free plan' });
    }

    await Subscription.deletePlan(planId);

    res.json({ success: true, message: 'Plan deleted successfully' });
  } catch (error) {
    console.error('Delete plan error:', error);
    res.status(500).json({ error: 'Failed to delete plan' });
  }
});

// Get plan details
router.get('/plans/:planId', requireAdmin, async (req, res) => {
  try {
    const { planId } = req.params;

    const plan = await Subscription.getPlanById(planId);
    if (!plan) {
      return res.status(404).json({ error: 'Plan not found' });
    }

    res.json({ success: true, plan });
  } catch (error) {
    console.error('Get plan details error:', error);
    res.status(500).json({ error: 'Failed to get plan details' });
  }
});

// Get plan subscribers
router.get('/plans/:planId/subscribers', requireAdmin, async (req, res) => {
  try {
    const { planId } = req.params;

    const plan = await Subscription.getPlanById(planId);
    if (!plan) {
      return res.status(404).json({ error: 'Plan not found' });
    }

    const subscribers = await Subscription.getPlanSubscribers(planId, plan.name);

    res.json({
      success: true,
      plan,
      subscribers
    });
  } catch (error) {
    console.error('Get plan subscribers error:', error);
    res.status(500).json({ error: 'Failed to get plan subscribers' });
  }
});

// Bulk operations for plans
router.post('/plans/bulk-action', requireAdmin, async (req, res) => {
  try {
    const { action, planIds } = req.body;

    if (!action || !Array.isArray(planIds) || planIds.length === 0) {
      return res.status(400).json({ error: 'Action and plan IDs are required' });
    }

    let results = [];

    switch (action) {
      case 'delete':
        for (const planId of planIds) {
          try {
            const plan = await Subscription.getPlanById(planId);
            if (plan && plan.name.toLowerCase() !== 'free' && !plan.name.toLowerCase().includes('free')) {
              await Subscription.deletePlan(planId);
              results.push({ planId, success: true });
            } else {
              results.push({ planId, success: false, error: 'Cannot delete free plan' });
            }
          } catch (error) {
            results.push({ planId, success: false, error: error.message });
          }
        }
        break;

      case 'activate':
        for (const planId of planIds) {
          try {
            await Subscription.updatePlanStatus(planId, true);
            results.push({ planId, success: true });
          } catch (error) {
            results.push({ planId, success: false, error: error.message });
          }
        }
        break;

      case 'deactivate':
        for (const planId of planIds) {
          try {
            await Subscription.updatePlanStatus(planId, false);
            results.push({ planId, success: true });
          } catch (error) {
            results.push({ planId, success: false, error: error.message });
          }
        }
        break;

      default:
        return res.status(400).json({ error: 'Invalid action' });
    }

    res.json({ success: true, results });
  } catch (error) {
    console.error('Bulk action error:', error);
    res.status(500).json({ error: 'Failed to perform bulk action' });
  }
});

// Bulk operations for users
router.post('/users/bulk-action', requireAdmin, async (req, res) => {
  try {
    const { action, userIds } = req.body;

    if (!action || !Array.isArray(userIds) || userIds.length === 0) {
      return res.status(400).json({ error: 'Action and user IDs are required' });
    }

    let results = [];

    switch (action) {
      case 'activate':
        for (const userId of userIds) {
          try {
            await User.updateActiveStatus(userId, true);
            results.push({ userId, success: true });
          } catch (error) {
            results.push({ userId, success: false, error: error.message });
          }
        }
        break;

      case 'deactivate':
        for (const userId of userIds) {
          try {
            await User.updateActiveStatus(userId, false);
            results.push({ userId, success: true });
          } catch (error) {
            results.push({ userId, success: false, error: error.message });
          }
        }
        break;

      case 'change_plan':
        const { planId } = req.body;
        if (!planId) {
          return res.status(400).json({ error: 'Plan ID is required for plan change' });
        }

        const plan = await Subscription.getPlanById(planId);
        if (!plan) {
          return res.status(404).json({ error: 'Plan not found' });
        }

        for (const userId of userIds) {
          try {
            await User.updatePlan(userId, plan.name, plan.max_streaming_slots, plan.max_storage_gb);
            results.push({ userId, success: true });
          } catch (error) {
            results.push({ userId, success: false, error: error.message });
          }
        }
        break;

      default:
        return res.status(400).json({ error: 'Invalid action' });
    }

    res.json({ success: true, results });
  } catch (error) {
    console.error('Bulk user action error:', error);
    res.status(500).json({ error: 'Failed to perform bulk action' });
  }
});

// System statistics
router.get('/stats', requireAdmin, async (req, res) => {
  try {
    const stats = await getSystemStats();
    res.json(stats);
  } catch (error) {
    console.error('System stats error:', error);
    res.status(500).json({ error: 'Failed to get system statistics' });
  }
});

// Load balancer management page
router.get('/load-balancer', requireAdmin, async (req, res) => {
  try {
    const loadBalancer = require('../services/loadBalancer');
    const status = loadBalancer.getStatus();
    const metrics = loadBalancer.getMetrics();

    res.render('admin/load-balancer', {
      title: 'Load Balancer Management',
      active: 'admin',
      status,
      metrics
    });
  } catch (error) {
    console.error('Load balancer page error:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load load balancer page',
      error: error
    });
  }
});

// Get user details with quota info
router.get('/users/:userId', requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({
      success: true,
      user
    });
  } catch (error) {
    console.error('Get user details error:', error);
    res.status(500).json({ error: 'Failed to get user details' });
  }
});

// Edit user
router.post('/users/edit', requireAdmin, async (req, res) => {
  try {
    const {
      userId,
      username,
      email,
      role,
      plan,
      max_streaming_slots,
      max_storage_gb,
      is_active,
      new_password
    } = req.body;

    if (!userId || !username) {
      return res.status(400).json({ error: 'User ID and username are required' });
    }

    // Check if user exists
    const existingUser = await User.findById(userId);
    if (!existingUser) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Handle storage conversion (frontend already converts MB to GB)
    const storageGB = max_storage_gb || 2;

    // Update user data
    const updateData = {
      username,
      email: email || null,
      role: role || 'user',
      plan_type: plan || 'Preview',
      max_streaming_slots: max_streaming_slots || 0,
      max_storage_gb: storageGB,
      is_active: is_active ? 1 : 0
    };

    // Update password if provided
    if (new_password) {
      const bcrypt = require('bcrypt');
      updateData.password = await bcrypt.hash(new_password, 10);
    }

    await User.updateUser(userId, updateData);

    res.json({ success: true, message: 'User updated successfully' });
  } catch (error) {
    console.error('Edit user error:', error);
    res.status(500).json({ error: 'Failed to update user' });
  }
});

// Helper function to get system statistics
async function getSystemStats() {
  const { db } = require('../db/database');

  return new Promise((resolve, reject) => {
    db.all(`
      SELECT
        (SELECT COUNT(*) FROM users) as total_users,
        (SELECT COUNT(*) FROM users WHERE is_active = 1) as active_users,
        (SELECT COUNT(*) FROM streams) as total_streams,
        (SELECT COUNT(*) FROM streams WHERE status = 'live') as live_streams,
        (SELECT COUNT(*) FROM videos) as total_videos,
        (SELECT SUM(file_size) FROM videos) as total_storage_bytes,
        (SELECT COUNT(*) FROM users WHERE plan_type != 'Preview' AND plan_type IS NOT NULL) as active_subscriptions
    `, [], (err, rows) => {
      if (err) {
        reject(err);
      } else {
        const stats = rows[0];
        stats.total_storage_gb = stats.total_storage_bytes ?
          (stats.total_storage_bytes / (1024 * 1024 * 1024)).toFixed(2) : 0;
        resolve(stats);
      }
    });
  });
}

module.exports = router;
