<%- include('../partials/header') %>

<div class="min-h-screen bg-dark-900 text-white">
  <%- include('../partials/navbar') %>
  
  <div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
      <div>
        <h1 class="text-3xl font-bold text-white">Load Balancer Management</h1>
        <p class="text-gray-400 mt-2">Monitor and configure automatic quality adjustment based on CPU usage</p>
      </div>
      <div class="flex items-center space-x-4">
        <div class="flex items-center space-x-2">
          <div class="w-3 h-3 rounded-full <%= status.isRunning ? 'bg-green-400' : 'bg-red-400' %>"></div>
          <span class="text-sm font-medium"><%= status.isRunning ? 'Running' : 'Stopped' %></span>
        </div>
        <button id="toggleLoadBalancer" 
                class="px-4 py-2 rounded-lg font-medium transition-colors <%= status.isRunning ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700' %>">
          <%= status.isRunning ? 'Stop' : 'Start' %> Load Balancer
        </button>
      </div>
    </div>

    <!-- Status Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Current CPU -->
      <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-gray-400 text-sm">Current CPU</p>
            <p class="text-2xl font-bold text-white"><span id="current-cpu"><%= metrics.currentCpu %></span>%</p>
          </div>
          <div class="w-12 h-12 bg-orange-500/20 rounded-lg flex items-center justify-center">
            <i class="ti ti-cpu text-orange-400 text-xl"></i>
          </div>
        </div>
        <div class="w-full bg-gray-700 rounded-full h-2.5 mt-4">
          <div id="cpu-bar" class="bg-orange-400 h-2.5 rounded-full transition-all duration-300" style="width: <%= metrics.currentCpu %>%"></div>
        </div>
      </div>

      <!-- Current Quality -->
      <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-gray-400 text-sm">Current Quality</p>
            <p class="text-2xl font-bold text-white" id="current-quality"><%= metrics.currentQualityLevel %></p>
          </div>
          <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
            <i class="ti ti-video text-blue-400 text-xl"></i>
          </div>
        </div>
      </div>

      <!-- Active Streams -->
      <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-gray-400 text-sm">Active Streams</p>
            <p class="text-2xl font-bold text-white" id="active-streams"><%= metrics.activeStreams %></p>
          </div>
          <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
            <i class="ti ti-broadcast text-green-400 text-xl"></i>
          </div>
        </div>
      </div>

      <!-- Quality Changes -->
      <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-gray-400 text-sm">Quality Changes</p>
            <p class="text-2xl font-bold text-white" id="quality-changes"><%= metrics.totalQualityChanges %></p>
          </div>
          <div class="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
            <i class="ti ti-adjustments text-purple-400 text-xl"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- Configuration and Controls -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
      <!-- Configuration Panel -->
      <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
        <h3 class="text-xl font-semibold text-white mb-4">Configuration</h3>
        
        <form id="configForm" class="space-y-4">
          <!-- Enable/Disable -->
          <div class="flex items-center justify-between">
            <label class="text-sm font-medium text-gray-300">Auto Load Balancing</label>
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" id="enableLoadBalancing" class="sr-only peer" <%= status.config.enabled ? 'checked' : '' %>>
              <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
            </label>
          </div>

          <!-- CPU Thresholds -->
          <div class="space-y-3">
            <label class="text-sm font-medium text-gray-300">CPU Thresholds</label>
            
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="text-xs text-gray-400">Low (Medium Quality)</label>
                <input type="number" id="thresholdLow" min="0" max="100" 
                       value="<%= status.config.cpuThresholds.LOW %>"
                       class="w-full px-3 py-2 bg-dark-700 border border-gray-600 rounded-lg text-white text-sm">
              </div>
              <div>
                <label class="text-xs text-gray-400">Medium (Low Quality)</label>
                <input type="number" id="thresholdMedium" min="0" max="100" 
                       value="<%= status.config.cpuThresholds.MEDIUM %>"
                       class="w-full px-3 py-2 bg-dark-700 border border-gray-600 rounded-lg text-white text-sm">
              </div>
              <div>
                <label class="text-xs text-gray-400">High (Minimal Quality)</label>
                <input type="number" id="thresholdHigh" min="0" max="100" 
                       value="<%= status.config.cpuThresholds.HIGH %>"
                       class="w-full px-3 py-2 bg-dark-700 border border-gray-600 rounded-lg text-white text-sm">
              </div>
              <div>
                <label class="text-xs text-gray-400">Check Interval (seconds)</label>
                <input type="number" id="checkInterval" min="5" max="60" 
                       value="<%= status.config.checkInterval / 1000 %>"
                       class="w-full px-3 py-2 bg-dark-700 border border-gray-600 rounded-lg text-white text-sm">
              </div>
            </div>
          </div>

          <button type="submit" class="w-full bg-primary hover:bg-primary-dark text-white font-medium py-2 px-4 rounded-lg transition-colors">
            Update Configuration
          </button>
        </form>
      </div>

      <!-- Manual Quality Control -->
      <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
        <h3 class="text-xl font-semibold text-white mb-4">Manual Quality Control</h3>
        
        <div class="space-y-4">
          <p class="text-sm text-gray-400">Override automatic quality adjustment for all active streams</p>
          
          <div class="grid grid-cols-2 gap-3">
            <button onclick="setQuality('NORMAL')" class="quality-btn bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition-colors">
              <div class="text-sm">NORMAL</div>
              <div class="text-xs opacity-75">720p+</div>
            </button>
            <button onclick="setQuality('MEDIUM')" class="quality-btn bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-3 px-4 rounded-lg transition-colors">
              <div class="text-sm">MEDIUM</div>
              <div class="text-xs opacity-75">480p</div>
            </button>
            <button onclick="setQuality('LOW')" class="quality-btn bg-orange-600 hover:bg-orange-700 text-white font-medium py-3 px-4 rounded-lg transition-colors">
              <div class="text-sm">LOW</div>
              <div class="text-xs opacity-75">360p</div>
            </button>
            <button onclick="setQuality('MINIMAL')" class="quality-btn bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-4 rounded-lg transition-colors">
              <div class="text-sm">MINIMAL</div>
              <div class="text-xs opacity-75">240p</div>
            </button>
          </div>
          
          <div class="text-xs text-gray-500 mt-2">
            * Manual override disables auto-balancing for 5 minutes
          </div>
        </div>
      </div>
    </div>

    <!-- Quality Levels Reference -->
    <div class="bg-dark-800 rounded-lg p-6 border border-gray-700 mb-8">
      <h3 class="text-xl font-semibold text-white mb-4">Quality Levels Reference</h3>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div class="bg-dark-700 rounded-lg p-4">
          <div class="flex items-center space-x-2 mb-2">
            <div class="w-3 h-3 bg-green-400 rounded-full"></div>
            <span class="font-medium text-green-400">NORMAL</span>
          </div>
          <div class="text-sm text-gray-300 space-y-1">
            <div>Resolution: 1280x720</div>
            <div>Bitrate: 4000 kbps</div>
            <div>FPS: 30</div>
            <div class="text-xs text-gray-400">CPU < 60%</div>
          </div>
        </div>
        
        <div class="bg-dark-700 rounded-lg p-4">
          <div class="flex items-center space-x-2 mb-2">
            <div class="w-3 h-3 bg-yellow-400 rounded-full"></div>
            <span class="font-medium text-yellow-400">MEDIUM</span>
          </div>
          <div class="text-sm text-gray-300 space-y-1">
            <div>Resolution: 720x480</div>
            <div>Bitrate: 2500 kbps</div>
            <div>FPS: 30</div>
            <div class="text-xs text-gray-400">CPU 60-75%</div>
          </div>
        </div>
        
        <div class="bg-dark-700 rounded-lg p-4">
          <div class="flex items-center space-x-2 mb-2">
            <div class="w-3 h-3 bg-orange-400 rounded-full"></div>
            <span class="font-medium text-orange-400">LOW</span>
          </div>
          <div class="text-sm text-gray-300 space-y-1">
            <div>Resolution: 480x360</div>
            <div>Bitrate: 1500 kbps</div>
            <div>FPS: 30</div>
            <div class="text-xs text-gray-400">CPU 75-85%</div>
          </div>
        </div>
        
        <div class="bg-dark-700 rounded-lg p-4">
          <div class="flex items-center space-x-2 mb-2">
            <div class="w-3 h-3 bg-red-400 rounded-full"></div>
            <span class="font-medium text-red-400">MINIMAL</span>
          </div>
          <div class="text-sm text-gray-300 space-y-1">
            <div>Resolution: 360x240</div>
            <div>Bitrate: 800 kbps</div>
            <div>FPS: 24</div>
            <div class="text-xs text-gray-400">CPU > 85%</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Activity Log -->
    <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
      <h3 class="text-xl font-semibold text-white mb-4">Recent Activity</h3>
      
      <div id="activity-log" class="space-y-2 max-h-64 overflow-y-auto">
        <% if (status.stats.streamingServiceStatus.recentLogs && status.stats.streamingServiceStatus.recentLogs.length > 0) { %>
          <% status.stats.streamingServiceStatus.recentLogs.forEach(log => { %>
            <div class="text-sm text-gray-300 font-mono bg-dark-700 rounded px-3 py-2">
              <%= log %>
            </div>
          <% }) %>
        <% } else { %>
          <div class="text-sm text-gray-500 italic">No recent activity</div>
        <% } %>
      </div>
    </div>
  </div>
</div>

<script>
// Auto-refresh data every 5 seconds
setInterval(updateMetrics, 5000);

// Update metrics
async function updateMetrics() {
  try {
    const response = await fetch('/api/load-balancer/metrics');
    const metrics = await response.json();
    
    document.getElementById('current-cpu').textContent = metrics.currentCpu;
    document.getElementById('cpu-bar').style.width = metrics.currentCpu + '%';
    document.getElementById('current-quality').textContent = metrics.currentQualityLevel;
    document.getElementById('active-streams').textContent = metrics.activeStreams;
    document.getElementById('quality-changes').textContent = metrics.totalQualityChanges;
  } catch (error) {
    console.error('Failed to update metrics:', error);
  }
}

// Toggle load balancer
document.getElementById('toggleLoadBalancer').addEventListener('click', async function() {
  const isRunning = this.textContent.trim().startsWith('Stop');
  const endpoint = isRunning ? '/api/load-balancer/stop' : '/api/load-balancer/start';
  
  try {
    const response = await fetch(endpoint, { method: 'POST' });
    const result = await response.json();
    
    if (result.success) {
      location.reload();
    } else {
      alert('Error: ' + result.error);
    }
  } catch (error) {
    alert('Failed to toggle load balancer: ' + error.message);
  }
});

// Configuration form
document.getElementById('configForm').addEventListener('submit', async function(e) {
  e.preventDefault();
  
  const config = {
    enabled: document.getElementById('enableLoadBalancing').checked,
    checkInterval: parseInt(document.getElementById('checkInterval').value) * 1000,
    cpuThresholds: {
      LOW: parseInt(document.getElementById('thresholdLow').value),
      MEDIUM: parseInt(document.getElementById('thresholdMedium').value),
      HIGH: parseInt(document.getElementById('thresholdHigh').value)
    }
  };
  
  try {
    const response = await fetch('/api/load-balancer/config', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(config)
    });
    
    const result = await response.json();
    
    if (result.success) {
      alert('Configuration updated successfully!');
    } else {
      alert('Error: ' + result.error);
    }
  } catch (error) {
    alert('Failed to update configuration: ' + error.message);
  }
});

// Manual quality control
async function setQuality(level) {
  if (!confirm(`Set all active streams to ${level} quality? This will temporarily disable auto-balancing for 5 minutes.`)) {
    return;
  }
  
  try {
    const response = await fetch('/api/load-balancer/quality', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ qualityLevel: level })
    });
    
    const result = await response.json();
    
    if (result.success) {
      alert(result.message);
      updateMetrics();
    } else {
      alert('Error: ' + result.error);
    }
  } catch (error) {
    alert('Failed to set quality: ' + error.message);
  }
}
</script>

<%- include('../partials/footer') %>
